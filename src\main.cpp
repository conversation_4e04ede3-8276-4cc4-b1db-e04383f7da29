/*
 * IEC104Server Usage Example
 * 
 * This example shows how to use the IEC104Server class
 * without W5500 initialization code mixed in.
 * 
 * You need to initialize your network connection separately
 * before using the IEC104Server.
 */

 #include <Arduino.h>
 #include "Ethernet.h"
 #include <SPI.h>
 #include "IEC104Server.h"
 
 // W5500 pin definitions (adjust according to your hardware)
 #define ETH_MISO 13
 #define ETH_SCK 12
 #define ETH_MOSI 11
 #define ETH_CS 10
 #define ETH_RST 9
 #define ETH_INT 14
 
 #define PIN_SPI_SS ETH_CS
 #define PIN_ETHERNET_RESET ETH_RST
 
 // Global variables
 IEC104Server iec104Server;
 unsigned long lastPeriodicSend = 0;
 const unsigned long PERIODIC_INTERVAL = 1 * 1000; // 5 seconds
 
 // Initialize Ethernet hardware
 void initializeEthernet() {
     pinMode(PIN_ETHERNET_RESET, OUTPUT);
     digitalWrite(PIN_ETHERNET_RESET, LOW);
     delay(100);
     digitalWrite(PIN_ETHERNET_RESET, HIGH);
 
     Ethernet.init(PIN_SPI_SS);
     SPI.begin(ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
     Serial0.printf("SPI.begin(SCK=%d, MISO=%d, MOSI=%d, NSS=%d)\n", 
                   ETH_SCK, ETH_MISO, ETH_MOSI, ETH_CS);
     SPI.setFrequency(8000000); // 8MHz SPI frequency
 }
 
 void getMacAddr(uint8_t *dmac) {
     assert(esp_efuse_mac_get_default(dmac) == ESP_OK);
 }
 
 // Start Ethernet connection with DHCP
 bool startEthernetConnection() {
     uint8_t mac[6];
     getMacAddr(mac);
     mac[0] &= 0xfe;
 
     Serial0.println("Starting Ethernet DHCP...");
     int status = Ethernet.begin(mac);
 
     return (status != 0);
 }
 
 void handleEthernetError() {
     if (Ethernet.hardwareStatus() == EthernetNoHardware) {
         Serial0.println("Ethernet shield was not found");
     } else if (Ethernet.linkStatus() == LinkOFF) {
         Serial0.println("Ethernet cable is not connected");
     } else {
         Serial0.println("Unknown Ethernet error");
     }
 }
 
 // Print network information
 void printNetworkInfo() {
     Serial0.printf("Local IP %u.%u.%u.%u\n", 
                   Ethernet.localIP()[0], Ethernet.localIP()[1], 
                   Ethernet.localIP()[2], Ethernet.localIP()[3]);
     Serial0.printf("Subnet Mask %u.%u.%u.%u\n", 
                   Ethernet.subnetMask()[0], Ethernet.subnetMask()[1], 
                   Ethernet.subnetMask()[2], Ethernet.subnetMask()[3]);
     Serial0.printf("Gateway IP %u.%u.%u.%u\n", 
                   Ethernet.gatewayIP()[0], Ethernet.gatewayIP()[1], 
                   Ethernet.gatewayIP()[2], Ethernet.gatewayIP()[3]);
     Serial0.printf("DNS Server IP %u.%u.%u.%u\n", 
                   Ethernet.dnsServerIP()[0], Ethernet.dnsServerIP()[1], 
                   Ethernet.dnsServerIP()[2], Ethernet.dnsServerIP()[3]);
     Serial0.println("Ethernet connection successful!");
 }
 
 void setup() {
     // Initialize serial communication
     Serial0.begin(115200);
     while (!Serial0) {
         ; // wait for serial port to connect
     }
 
     Serial0.println("ESP32 IEC104 Server Example");
     Serial0.println("Starting initialization...");
 
     // Initialize Ethernet
     initializeEthernet();
 
     if (!startEthernetConnection()) {
         handleEthernetError();
         return;
     }
 
     printNetworkInfo();
 
     // Get local IP address as string
     IPAddress ip = Ethernet.localIP();
     char ipStr[16];
     snprintf(ipStr, sizeof(ipStr), "%u.%u.%u.%u", ip[0], ip[1], ip[2], ip[3]);
 
     // Initialize IEC104 server
     if (!iec104Server.initialize(ipStr, 4, 10)) {
         Serial0.println("Failed to initialize IEC104 server");
         return;
     }
 
     // Optional: Enable raw message logging for debugging
     // iec104Server.enableRawMessageLogging(true);
 
     // Start the IEC104 server
     if (!iec104Server.start()) {
         Serial0.println("Failed to start IEC104 server");
         return;
     }
 
     Serial0.println("IEC104 server is running and ready for connections");
 }
 
 void loop() {
     // Send periodic data every 5 seconds
     unsigned long currentTime = millis();
     if (currentTime - lastPeriodicSend >= PERIODIC_INTERVAL &&  iec104Server.getConnectedClients()){
        
         // Send additional measured values
         static int16_t testValue = 1000;
         iec104Server.sendMeasuredValue(200, testValue++);
 
        //  // Send single point information
        //  static bool testBool = false;
        //  iec104Server.sendSinglePointInfo(201, testBool);
        //  testBool = !testBool;
 
        //  // Send bit string
        //  static uint32_t testBits = 0x12345678;
        //  iec104Server.sendBitString32(202, testBits++);
 
         lastPeriodicSend = currentTime;
     }
     else if (!iec104Server.getConnectedClients()){
        
     }
 
     // Small delay to prevent excessive CPU usage
     delay(10);
 }
 